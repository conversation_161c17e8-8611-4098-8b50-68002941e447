# auto detects a good number of processes to run
worker_processes auto;

#Provides the configuration file context in which the directives that affect connection processing are specified.
events {
    # Sets the maximum number of simultaneous connections that can be opened by a worker process.
    worker_connections 8000;
    # Tells the worker to accept multiple connections at a time
    multi_accept on;
}


http {
    proxy_headers_hash_max_size 1024;
    proxy_headers_hash_bucket_size 128;


    # what times to include
    include /etc/nginx/mime.types;
    # what is the default one
    default_type application/octet-stream;

    # Sets the path, format, and configuration for a buffered log write
    log_format compression '$remote_addr - $remote_user [$time_local] '
    '"$request" $status $upstream_addr '
    '"$http_referer" "$http_user_agent"';


    map $http_origin $cors_allow_origin {
        default "";
        "~^https?://(cdgtaxi\.com\.sg|([a-z0-9.-]+)\.cdgtaxi\.com\.sg)$" $http_origin;
        "~^https?://([a-z0-9.-]+)\.googleapis\.com$" $http_origin;
    }


    server {
        listen 443 ssl;
    #    server_name cabchargeasiauat.cdgtaxi.com.sg sitcabchargeasia.cdgtaxi.com.sg;
        server_name sitcabchargeasia.cdgtaxi.com.sg cabchargeasia.cdgtaxi.com.sg cabchargeasiauat.cdgtaxi.com.sg;


        # Remove the 'X-Frame-Options: DENY' header set by Spring Boot
        # proxy_hide_header Strict-Transport-Security;
        # proxy_hide_header Content-Security-Policy;
        # proxy_hide_header X-Frame-Options;
        # proxy_hide_header X-Content-Type-Options;
        # proxy_hide_header Referrer-Policy;

        # add security headers
        # Add Strict-Transport-Security header (response only)
        # add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        # Add Content-Security-Policy header (response only)
        # add_header Content-Security-Policy "default-src 'none'; script-src 'self' *.cdgtaxi.com.sg *.googleapis.com 'nonce-random123' 'strict-dynamic'; style-src 'self'; img-src 'self' *.cdgtaxi.com.sg *.googleapis.com data:; font-src 'self'; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self'; connect-src 'self'; upgrade-insecure-requests; block-all-mixed-content; require-trusted-types-for 'script';" always;


        ssl_certificate cdg.crt;

        # generate key by command line: openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365
        # decrypted key from key.pem to decrypted_key.pem
        ssl_certificate_key cdg.key ;

       # SSL/TLS configuration - TLS 1.2 only
        ssl_protocols TLSv1.2;

        # Strong cipher suites - prioritizes ECDHE for Perfect Forward Secrecy
        # Excludes weak RSA key exchange and prioritizes AEAD ciphers
        ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256';

        # Prefer server cipher order for TLS 1.2
        ssl_prefer_server_ciphers on;

        # Additional security settings
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_session_tickets off;

        # OCSP stapling (disabled - requires full certificate chain)
        # ssl_stapling on;
        # ssl_stapling_verify on;


        # listen 80;
        # save logs here
        access_log /var/log/nginx/access.log compression;

        # where the root here
        root /usr/share/nginx/html;
        # what file to server as index
        index index.html index.htm;

        # Default Cache-Control for general responses
        #add_header Cache-Control "public";
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;

        # Add Strict-Transport-Security header (response only)
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Add Content-Security-Policy header (response only)
        #add_header Content-Security-Policy "default-src 'none'; script-src 'self' *.cdgtaxi.com.sg *.googleapis.com 'nonce-random123' 'strict-dynamic'; style-src 'self'; img-src 'self' *.cdgtaxi.com.sg *.googleapis.com data:; font-src 'self'; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self'; connect-src 'self'; upgrade-insecure-requests; block-all-mixed-content; require-trusted-types-for 'script';" always;


        # Add X-Frame-Options header (response only)
        add_header X-Frame-Options "SAMEORIGIN" always;

        # Add X-Content-Type-Options header (response only)
        add_header X-Content-Type-Options "nosniff" always;

        # Add Referrer-Policy header (response only)
        add_header Referrer-Policy "origin-when-cross-origin" always;


        # Error handling
        error_page 404 /404.html;
        error_page *********** 504 /50x.html;

        location = /404.html {
            root /usr/share/nginx/html;
        }

        location = /50x.html {
            root /usr/share/nginx/html;
        }

        location = /index.html {
            try_files $uri =404;
            #add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            add_header Cache-Control "public, max-age=300"; # 5 minutes cache
            #add_header Pragma "no-cache" always;
            #add_header Expires "0" always;

            add_header X-Content-Type-Options "nosniff" always;
        }


        # Manifest file
        location = /manifest.webmanifest {
            add_header Content-Type application/manifest+json;
            add_header Cache-Control "public, max-age=300" always; # 5 minutes cache
        }

        location = /manifest.json {
            add_header Content-Type application/manifest+json;
            add_header Cache-Control "public, max-age=86400" always; # 1 day cache
        }

        # Static files - must come BEFORE generic proxy location
        location ~* \.(js|css|json|png|jpg|svg|woff|woff2|ttf)$ {
            try_files $uri =404;

            add_header X-Content-Type-Options "nosniff" always;

            # Aggressive no-cache headers to override Cloudflare
            add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0" always;
            add_header Pragma "no-cache" always;
            add_header Expires "Thu, 01 Jan 1970 00:00:00 GMT" always;

            # Remove ETag and Last-Modified to prevent conditional requests
            add_header Last-Modified "" always;
            add_header ETag "" always;

            # Force Cloudflare bypass
            add_header CF-Cache-Status "BYPASS" always;
            add_header X-Cache-Control "no-cache" always;

            access_log off;
        }

        # Specific handling for the problematic image file
        location = /assets/images/DTK_3657.jpg {
            try_files $uri =404;
            add_header X-Content-Type-Options "nosniff" always;

            # Maximum strength no-cache headers
            add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0, s-maxage=0" always;
            add_header Pragma "no-cache" always;
            add_header Expires "Thu, 01 Jan 1970 00:00:00 GMT" always;

            # Remove all caching-related headers
            add_header Last-Modified "" always;
            add_header ETag "" always;

            # Force Cloudflare bypass with multiple headers
            add_header CF-Cache-Status "BYPASS" always;
            add_header X-Cache-Control "no-cache" always;
            add_header Surrogate-Control "no-store" always;

            # Add timestamp to force refresh
            add_header X-Timestamp $msec always;

            access_log on;
        }

        # Handle assets directory specifically
        location ~* ^/assets/ {
            try_files $uri =404;
            add_header X-Content-Type-Options "nosniff" always;

            # Force no caching with multiple headers to override Cloudflare
            add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0" always;
            add_header Pragma "no-cache" always;
            add_header Expires "Thu, 01 Jan 1970 00:00:00 GMT" always;

            # Add ETag removal to prevent conditional requests
            add_header Last-Modified "" always;
            add_header ETag "" always;

            # Force Cloudflare to bypass cache with custom header
            add_header CF-Cache-Status "BYPASS" always;
            add_header X-Cache-Control "no-cache" always;

            access_log off;
        }

        location = / {
            # Handle the root URL here.
            # First attempt to serve request as file, then
            # as directory, then fall back to redirecting to index.html
            try_files $uri $uri/ /portal-ai/index.html;

            add_header Cache-Control "public, max-age=300"; # 5 minutes cache
            #add_header Cache-Control "no-cache, no-store, must-revalidate" always;

            #add_header X-Content-Type-Options "nosniff" always;
        }


        location / {
            # Handle the api URL here.
            proxy_pass http://************:15000$request_uri;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }


        location ~ ^/(api|token|cabcharge|enquiry)(/|$) {


            proxy_pass http://************:15000$request_uri;


            # Remove the 'X-Frame-Options: DENY' header set by Spring Boot
            proxy_hide_header Strict-Transport-Security;
            proxy_hide_header Content-Security-Policy;
            proxy_hide_header X-Frame-Options;
            proxy_hide_header X-Content-Type-Options;
            proxy_hide_header Referrer-Policy;
            proxy_hide_header Access-Control-Allow-Origin;

            proxy_cookie_path / "/; Secure; HttpOnly; SameSite=Strict";

            # Forward client auth/cookies
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Authorization $http_authorization;
            proxy_set_header Cookie $http_cookie;


            # Secure CORS headers
            add_header Access-Control-Allow-Origin $cors_allow_origin always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
            add_header Access-Control-Allow-Credentials true always;


            # # Add Strict-Transport-Security header (response only)
            # add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
            # # Add Content-Security-Policy header (response only)
            # add_header Content-Security-Policy "default-src 'none'; script-src 'self' *.cdgtaxi.com.sg *.googleapis.com 'nonce-random123' 'strict-dynamic'; style-src 'self'; img-src 'self' *.cdgtaxi.com.sg *.googleapis.com data:; font-src 'self'; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self'; connect-src 'self'; upgrade-insecure-requests; block-all-mixed-content; require-trusted-types-for 'script';" always;
            # # Add X-Frame-Options header (response only)
            # add_header X-Frame-Options "SAMEORIGIN" always;

            # Add X-Content-Type-Options header (response only)
           # add_header X-Content-Type-Options "nosniff" always;
  

            # # Add Referrer-Policy header (response only)
            # add_header Referrer-Policy "origin-when-cross-origin" always;
            #
        }

        # Media: images, icons, video, audio, HTC
#        location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
#            expires 1M;
#            access_log off;
#        }

        # Javascript and CSS files
        # location ~* \.(?:css|js)$ {
        #location ~* \.(js|css|json|png|jpg|svg|woff|woff2|ttf)$ {
        #    try_files $uri =404;
        #    expires 1y;
        #    access_log off;
            #add_header Cache-Control "public, max-age=31536000, immutable";
      
            # Add X-Content-Type-Options header (response only)
        #    add_header X-Content-Type-Options "nosniff" always;
            
        #    add_header Expires "0" always;
        #}


        # Any route containing a file extension (e.g. /devicesfile.js)
#        location ~ ^.+\..+$ {
#            try_files $uri =404;
#        }


    }
}
