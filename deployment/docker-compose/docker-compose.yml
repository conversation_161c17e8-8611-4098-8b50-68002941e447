version: "3.9"

services:
  angular:
    container_name: angular
    
    #need to build image first by using: docker build -t nginx_ccp:1.0 . -f DockerfileOnPrem
    image: ccp_fe:2.03
    # build:
    #   context: ../
    #   dockerfile: ./Dockerfile
   
    ports:
      # - "80:80"
      - "443:443"
    networks:
      - cabcharge_net 
    volumes: 
      - ./nginx.conf:/etc/nginx/nginx.conf   
      - ./nginx-security-headers.conf:/etc/nginx/nginx-security-headers.conf
      - ./cdg.key:/etc/nginx/cdg.key 
      - ./cdg.crt:/etc/nginx/cdg.crt  
    restart: always 

networks:
  cabcharge_net: 
    driver: bridge
