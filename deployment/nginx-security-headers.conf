# Security Headers Snippet
# Include this snippet in location blocks that have other add_header directives
# Usage: include security-headers.conf;

# Add Strict-Transport-Security header (response only)
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

# Add X-Frame-Options header (response only)
add_header X-Frame-Options "SAMEORIGIN" always;

# Add X-Content-Type-Options header (response only)
add_header X-Content-Type-Options "nosniff" always;

# Add Referrer-Policy header (response only)
add_header Referrer-Policy "origin-when-cross-origin" always;

# Add X-Permitted-Cross-Domain-Policies header (response only)
add_header X-Permitted-Cross-Domain-Policies "none" always;

# Add Content-Security-Policy header (response only)
add_header Content-Security-Policy "default-src 'none'; script-src 'self' *.cdgtaxi.com.sg *.googleapis.com 'nonce-random123' 'unsafe-inline' 'strict-dynamic'; style-src 'self'; img-src 'self' *.cdgtaxi.com.sg *.googleapis.com data:; font-src 'self'; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self'; connect-src 'self'; upgrade-insecure-requests; block-all-mixed-content; require-trusted-types-for 'script';" always;

# Remove X-XSS-Protection header from all responses (deprecated and can introduce vulnerabilities)
# Using empty value to effectively remove the header
add_header X-XSS-Protection "" always;
